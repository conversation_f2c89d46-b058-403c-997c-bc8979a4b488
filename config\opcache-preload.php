<?php

/**
 * OPcache Preloading Script
 * 
 * Ce script précharge les fichiers PHP les plus utilisés en mémoire
 * pour améliorer les performances de l'application.
 */

if (!function_exists('opcache_compile_file')) {
    return;
}

$basePath = dirname(__DIR__);

// Fichiers Laravel core à précharger
$coreFiles = [
    '/vendor/laravel/framework/src/Illuminate/Foundation/Application.php',
    '/vendor/laravel/framework/src/Illuminate/Container/Container.php',
    '/vendor/laravel/framework/src/Illuminate/Support/ServiceProvider.php',
    '/vendor/laravel/framework/src/Illuminate/Http/Request.php',
    '/vendor/laravel/framework/src/Illuminate/Http/Response.php',
    '/vendor/laravel/framework/src/Illuminate/Routing/Router.php',
    '/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php',
    '/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php',
    '/vendor/laravel/framework/src/Illuminate/Support/Collection.php',
    '/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php',
];

// Fichiers de l'application à précharger
$appFiles = [
    '/app/Http/Controllers/Controller.php',
    '/app/Http/Controllers/Auth/AuthController.php',
    '/app/Http/Controllers/Pointage/PointageController.php',
    '/app/Http/Controllers/Site/SiteController.php',
    '/app/Http/Controllers/Employee/EmployeeController.php',
    '/app/Models/User.php',
    '/app/Models/Site.php',
    '/app/Models/Pointage.php',
    '/app/Models/Log.php',
    '/app/Http/Resources/UserResource.php',
    '/app/Http/Resources/SiteResource.php',
    '/app/Http/Resources/PointageResource.php',
    '/app/Http/Middleware/AdminMiddleware.php',
    '/app/Http/Middleware/SimpleAuthMiddleware.php',
    '/app/Http/Middleware/PerformanceMiddleware.php',
    '/app/Services/PerformanceMonitor.php',
    '/app/Providers/AppServiceProvider.php',
    '/app/Providers/PerformanceServiceProvider.php',
];

// Configuration files
$configFiles = [
    '/config/app.php',
    '/config/database.php',
    '/config/cache.php',
    '/config/session.php',
    '/config/queue.php',
    '/config/performance.php',
];

// Fonction pour précharger un fichier
function preloadFile(string $file): void
{
    if (file_exists($file) && is_readable($file)) {
        try {
            opcache_compile_file($file);
            echo "Preloaded: $file\n";
        } catch (Throwable $e) {
            echo "Failed to preload $file: " . $e->getMessage() . "\n";
        }
    } else {
        echo "File not found or not readable: $file\n";
    }
}

// Précharger les fichiers core de Laravel
echo "Preloading Laravel core files...\n";
foreach ($coreFiles as $file) {
    preloadFile($basePath . $file);
}

// Précharger les fichiers de l'application
echo "Preloading application files...\n";
foreach ($appFiles as $file) {
    preloadFile($basePath . $file);
}

// Précharger les fichiers de configuration
echo "Preloading configuration files...\n";
foreach ($configFiles as $file) {
    preloadFile($basePath . $file);
}

// Précharger automatiquement tous les modèles Eloquent
echo "Preloading Eloquent models...\n";
$modelsPath = $basePath . '/app/Models';
if (is_dir($modelsPath)) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($modelsPath)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            preloadFile($file->getPathname());
        }
    }
}

// Précharger les middlewares
echo "Preloading middlewares...\n";
$middlewarePath = $basePath . '/app/Http/Middleware';
if (is_dir($middlewarePath)) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($middlewarePath)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            preloadFile($file->getPathname());
        }
    }
}

// Précharger les resources
echo "Preloading API resources...\n";
$resourcesPath = $basePath . '/app/Http/Resources';
if (is_dir($resourcesPath)) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($resourcesPath)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            preloadFile($file->getPathname());
        }
    }
}

echo "OPcache preloading completed.\n";
