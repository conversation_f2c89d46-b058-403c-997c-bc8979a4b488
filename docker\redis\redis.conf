# Configuration Redis optimisée pour l'entreprise

# Configuration de base
bind 0.0.0.0
port 6379
timeout 0
tcp-keepalive 300

# Configuration de la sécurité
protected-mode no
# requirepass your_redis_password_here

# Configuration de la mémoire
maxmemory 1gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Configuration de la persistance
save 900 1
save 300 10
save 60 10000

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Configuration AOF (Append Only File)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Configuration des logs
loglevel notice
logfile /var/log/redis/redis.log
syslog-enabled no

# Configuration des clients
maxclients 10000

# Configuration des bases de données
databases 16

# Configuration de performance
tcp-backlog 511
hz 10

# Configuration des listes lentes
slowlog-log-slower-than 10000
slowlog-max-len 128

# Configuration de la latence
latency-monitor-threshold 100

# Configuration des notifications
notify-keyspace-events ""

# Configuration des hashs
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# Configuration des listes
list-max-ziplist-size -2
list-compress-depth 0

# Configuration des sets
set-max-intset-entries 512

# Configuration des sorted sets
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Configuration des HyperLogLog
hll-sparse-max-bytes 3000

# Configuration des streams
stream-node-max-bytes 4096
stream-node-max-entries 100

# Configuration de la réplication (si utilisée)
# replicaof <masterip> <masterport>
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Configuration du clustering (si utilisé)
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000

# Configuration de la sécurité avancée
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_REDIS
rename-command DEBUG ""

# Configuration des modules (si utilisés)
# loadmodule /path/to/module.so

# Configuration de la mémoire virtuelle (obsolète mais gardé pour compatibilité)
vm-enabled no
