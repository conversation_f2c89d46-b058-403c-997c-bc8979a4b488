<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

class SecurityMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifications de sécurité
        $this->checkSuspiciousActivity($request);
        $this->checkRateLimiting($request);
        $this->logSecurityEvent($request);

        $response = $next($request);

        // Ajouter les headers de sécurité
        $this->addSecurityHeaders($response);

        return $response;
    }

    /**
     * Vérifie les activités suspectes
     */
    private function checkSuspiciousActivity(Request $request): void
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        $path = $request->path();

        // Vérifier les tentatives d'injection SQL
        if ($this->detectSqlInjection($request)) {
            $this->logSecurityThreat($request, 'sql_injection_attempt');
            $this->blockIp($ip, 'SQL injection attempt');
        }

        // Vérifier les tentatives XSS
        if ($this->detectXssAttempt($request)) {
            $this->logSecurityThreat($request, 'xss_attempt');
            $this->blockIp($ip, 'XSS attempt');
        }

        // Vérifier les bots malveillants
        if ($this->detectMaliciousBot($userAgent)) {
            $this->logSecurityThreat($request, 'malicious_bot');
            $this->blockIp($ip, 'Malicious bot detected');
        }

        // Vérifier les tentatives d'accès aux fichiers sensibles
        if ($this->detectSensitiveFileAccess($path)) {
            $this->logSecurityThreat($request, 'sensitive_file_access');
            $this->blockIp($ip, 'Sensitive file access attempt');
        }
    }

    /**
     * Détecte les tentatives d'injection SQL
     */
    private function detectSqlInjection(Request $request): bool
    {
        $patterns = [
            '/(\bunion\b.*\bselect\b)/i',
            '/(\bselect\b.*\bfrom\b)/i',
            '/(\binsert\b.*\binto\b)/i',
            '/(\bdelete\b.*\bfrom\b)/i',
            '/(\bdrop\b.*\btable\b)/i',
            '/(\bupdate\b.*\bset\b)/i',
            '/(\bor\b.*1.*=.*1)/i',
            '/(\band\b.*1.*=.*1)/i',
            '/(\'.*or.*\'.*=.*\')/i',
            '/(\-\-)/i',
            '/(\/\*.*\*\/)/i',
        ];

        $input = json_encode($request->all());
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Détecte les tentatives XSS
     */
    private function detectXssAttempt(Request $request): bool
    {
        $patterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/<iframe[^>]*>.*?<\/iframe>/is',
            '/javascript:/i',
            '/on\w+\s*=/i',
            '/<img[^>]*src[^>]*>/i',
            '/eval\s*\(/i',
            '/expression\s*\(/i',
        ];

        $input = json_encode($request->all());
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Détecte les bots malveillants
     */
    private function detectMaliciousBot(string $userAgent): bool
    {
        $maliciousBots = [
            'sqlmap',
            'nikto',
            'nessus',
            'openvas',
            'w3af',
            'skipfish',
            'burp',
            'nmap',
            'masscan',
            'zap',
        ];

        $userAgent = strtolower($userAgent);
        
        foreach ($maliciousBots as $bot) {
            if (str_contains($userAgent, $bot)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Détecte les tentatives d'accès aux fichiers sensibles
     */
    private function detectSensitiveFileAccess(string $path): bool
    {
        $sensitivePaths = [
            '.env',
            '.git',
            'config/',
            'database/',
            'storage/',
            'vendor/',
            'composer.json',
            'composer.lock',
            'artisan',
            '.htaccess',
            'web.config',
            'phpinfo',
            'admin',
            'wp-admin',
            'wp-login',
        ];

        foreach ($sensitivePaths as $sensitivePath) {
            if (str_contains($path, $sensitivePath)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Vérifie le rate limiting
     */
    private function checkRateLimiting(Request $request): void
    {
        $ip = $request->ip();
        $path = $request->path();

        // Rate limiting spécifique par endpoint
        if (str_contains($path, 'api/auth/login')) {
            $key = 'login_attempts:' . $ip;
            $maxAttempts = config('performance.rate_limiting.login_attempts_per_minute', 5);
            
            if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
                $this->logSecurityThreat($request, 'rate_limit_exceeded');
                abort(429, 'Too many login attempts');
            }
            
            RateLimiter::hit($key, 60); // 1 minute
        }

        // Rate limiting général pour l'API
        if (str_contains($path, 'api/')) {
            $key = 'api_requests:' . $ip;
            $maxAttempts = config('performance.rate_limiting.api_requests_per_minute', 60);
            
            if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
                $this->logSecurityThreat($request, 'api_rate_limit_exceeded');
                abort(429, 'Too many API requests');
            }
            
            RateLimiter::hit($key, 60); // 1 minute
        }
    }

    /**
     * Bloque une adresse IP
     */
    private function blockIp(string $ip, string $reason): void
    {
        $key = 'blocked_ip:' . $ip;
        $duration = 3600; // 1 heure
        
        Cache::put($key, [
            'reason' => $reason,
            'blocked_at' => now()->toISOString(),
        ], $duration);

        Log::channel('security')->warning('IP blocked', [
            'ip' => $ip,
            'reason' => $reason,
            'duration' => $duration,
        ]);

        abort(403, 'Access denied');
    }

    /**
     * Enregistre un événement de sécurité
     */
    private function logSecurityEvent(Request $request): void
    {
        // Vérifier si l'IP est bloquée
        $ip = $request->ip();
        $blockedKey = 'blocked_ip:' . $ip;
        
        if (Cache::has($blockedKey)) {
            $blockInfo = Cache::get($blockedKey);
            Log::channel('security')->warning('Blocked IP attempted access', [
                'ip' => $ip,
                'path' => $request->path(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
                'block_info' => $blockInfo,
            ]);
            
            abort(403, 'Access denied');
        }

        // Log normal des accès sensibles
        if ($this->isSensitiveEndpoint($request->path())) {
            Log::channel('audit')->info('Sensitive endpoint access', [
                'ip' => $ip,
                'path' => $request->path(),
                'method' => $request->method(),
                'user_agent' => $request->userAgent(),
                'user_id' => $request->user()?->id,
            ]);
        }
    }

    /**
     * Enregistre une menace de sécurité
     */
    private function logSecurityThreat(Request $request, string $threatType): void
    {
        Log::channel('security')->critical('Security threat detected', [
            'threat_type' => $threatType,
            'ip' => $request->ip(),
            'path' => $request->path(),
            'method' => $request->method(),
            'user_agent' => $request->userAgent(),
            'input' => $request->all(),
            'headers' => $request->headers->all(),
            'user_id' => $request->user()?->id,
        ]);
    }

    /**
     * Vérifie si l'endpoint est sensible
     */
    private function isSensitiveEndpoint(string $path): bool
    {
        $sensitiveEndpoints = [
            'api/auth',
            'api/employees',
            'api/sites',
            'admin',
        ];

        foreach ($sensitiveEndpoints as $endpoint) {
            if (str_contains($path, $endpoint)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Ajoute les headers de sécurité
     */
    private function addSecurityHeaders(Response $response): void
    {
        if (config('performance.security.csrf_protection')) {
            $response->headers->set('X-Frame-Options', config('performance.security.x_frame_options', 'DENY'));
        }

        if (config('performance.security.xss_protection')) {
            $response->headers->set('X-XSS-Protection', '1; mode=block');
        }

        if (config('performance.security.content_security_policy')) {
            $response->headers->set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
        }

        if (config('performance.security.strict_transport_security')) {
            $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        }

        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    }
}
