FROM nginx:alpine

# Installer les dépendances
RUN apk add --no-cache \
    openssl \
    curl

# Copier la configuration Nginx
COPY docker/nginx/nginx.conf /etc/nginx/nginx.conf
COPY docker/nginx/sites-available/clockin.conf /etc/nginx/sites-available/clockin.conf

# Créer les liens symboliques
RUN ln -sf /etc/nginx/sites-available/clockin.conf /etc/nginx/sites-enabled/clockin.conf

# Créer les répertoires nécessaires
RUN mkdir -p /etc/nginx/ssl \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/cache/nginx

# Générer un certificat SSL auto-signé pour le développement
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/clockin.key \
    -out /etc/nginx/ssl/clockin.crt \
    -subj "/C=MA/ST=Casablanca/L=Casablanca/O=ClockIn/OU=IT/CN=localhost"

# Définir les permissions
RUN chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /var/log/nginx \
    && chmod -R 755 /var/cache/nginx

# Exposer les ports
EXPOSE 80 443

# Commande par défaut
CMD ["nginx", "-g", "daemon off;"]
