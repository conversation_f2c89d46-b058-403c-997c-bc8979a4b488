<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration des optimisations de performance pour l'environnement
    | d'entreprise. Ces paramètres permettent de configurer finement
    | les aspects de performance de l'application.
    |
    */

    'opcache' => [
        'enabled' => env('OPCACHE_ENABLED', true),
        'memory_consumption' => env('OPCACHE_MEMORY_CONSUMPTION', 256),
        'interned_strings_buffer' => env('OPCACHE_INTERNED_STRINGS_BUFFER', 16),
        'max_accelerated_files' => env('OPCACHE_MAX_ACCELERATED_FILES', 20000),
        'validate_timestamps' => env('OPCACHE_VALIDATE_TIMESTAMPS', false),
        'revalidate_freq' => env('OPCACHE_REVALIDATE_FREQ', 0),
        'save_comments' => env('OPCACHE_SAVE_COMMENTS', false),
        'enable_file_override' => env('OPCACHE_ENABLE_FILE_OVERRIDE', true),
        'optimization_level' => env('OPCACHE_OPTIMIZATION_LEVEL', '0x7FFEBFFF'),
        'jit' => env('OPCACHE_JIT', 'tracing'),
        'jit_buffer_size' => env('OPCACHE_JIT_BUFFER_SIZE', '128M'),
    ],

    'database' => [
        'query_cache' => env('DB_QUERY_CACHE', true),
        'query_cache_ttl' => env('DB_QUERY_CACHE_TTL', 3600),
        'connection_pooling' => env('DB_CONNECTION_POOLING', true),
        'lazy_loading' => env('DB_LAZY_LOADING', true),
        'eager_loading_limit' => env('DB_EAGER_LOADING_LIMIT', 100),
    ],

    'cache' => [
        'config_cache' => env('CONFIG_CACHE', true),
        'route_cache' => env('ROUTE_CACHE', true),
        'view_cache' => env('VIEW_CACHE', true),
        'event_cache' => env('EVENT_CACHE', true),
        'query_cache' => env('QUERY_CACHE', true),
        'api_cache_ttl' => env('API_CACHE_TTL', 300), // 5 minutes
        'static_cache_ttl' => env('STATIC_CACHE_TTL', 86400), // 24 heures
    ],

    'session' => [
        'gc_probability' => env('SESSION_GC_PROBABILITY', 1),
        'gc_divisor' => env('SESSION_GC_DIVISOR', 1000),
        'gc_maxlifetime' => env('SESSION_GC_MAXLIFETIME', 7200),
        'cookie_secure' => env('SESSION_SECURE_COOKIE', true),
        'cookie_httponly' => env('SESSION_HTTP_ONLY', true),
        'cookie_samesite' => env('SESSION_SAME_SITE', 'strict'),
    ],

    'compression' => [
        'enabled' => env('COMPRESSION_ENABLED', true),
        'level' => env('COMPRESSION_LEVEL', 6),
        'threshold' => env('COMPRESSION_THRESHOLD', 1024), // bytes
        'types' => [
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'application/json',
            'application/xml',
            'text/xml',
        ],
    ],

    'monitoring' => [
        'slow_query_threshold' => env('SLOW_QUERY_THRESHOLD', 1000), // ms
        'memory_limit_warning' => env('MEMORY_LIMIT_WARNING', 80), // %
        'cpu_limit_warning' => env('CPU_LIMIT_WARNING', 80), // %
        'disk_space_warning' => env('DISK_SPACE_WARNING', 85), // %
        'response_time_warning' => env('RESPONSE_TIME_WARNING', 2000), // ms
    ],

    'rate_limiting' => [
        'api_requests_per_minute' => env('API_RATE_LIMIT', 60),
        'login_attempts_per_minute' => env('LOGIN_RATE_LIMIT', 5),
        'password_reset_per_hour' => env('PASSWORD_RESET_RATE_LIMIT', 3),
        'pointage_requests_per_minute' => env('POINTAGE_RATE_LIMIT', 10),
    ],

    'security' => [
        'csrf_protection' => env('CSRF_PROTECTION', true),
        'sql_injection_protection' => env('SQL_INJECTION_PROTECTION', true),
        'xss_protection' => env('XSS_PROTECTION', true),
        'content_security_policy' => env('CSP_ENABLED', true),
        'strict_transport_security' => env('HSTS_ENABLED', true),
        'x_frame_options' => env('X_FRAME_OPTIONS', 'DENY'),
    ],

    'logging' => [
        'performance_logs' => env('PERFORMANCE_LOGS', true),
        'query_logs' => env('QUERY_LOGS', false),
        'slow_query_logs' => env('SLOW_QUERY_LOGS', true),
        'error_logs' => env('ERROR_LOGS', true),
        'access_logs' => env('ACCESS_LOGS', true),
        'audit_logs' => env('AUDIT_LOGS', true),
        'log_rotation' => env('LOG_ROTATION', true),
        'log_retention_days' => env('LOG_RETENTION_DAYS', 30),
    ],

];
