<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Http\Request;

class PerformanceServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        // Enregistrer les services de performance
        $this->app->singleton('performance.monitor', function ($app) {
            return new \App\Services\PerformanceMonitor();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Optimisations de base de données
        $this->configureDatabaseOptimizations();
        
        // Monitoring des performances
        $this->configurePerformanceMonitoring();
        
        // Cache des requêtes
        $this->configureQueryCache();
        
        // Optimisations de session
        $this->configureSessionOptimizations();
    }

    /**
     * Configure les optimisations de base de données
     */
    private function configureDatabaseOptimizations(): void
    {
        if (config('performance.database.query_cache')) {
            // Configuration du cache de requêtes
            DB::listen(function (QueryExecuted $query) {
                if ($query->time > config('performance.monitoring.slow_query_threshold', 1000)) {
                    Log::warning('Slow query detected', [
                        'sql' => $query->sql,
                        'bindings' => $query->bindings,
                        'time' => $query->time,
                        'connection' => $query->connectionName,
                    ]);
                }
            });
        }

        // Configuration des connexions persistantes
        if (config('performance.database.connection_pooling')) {
            config(['database.connections.mysql.options' => array_merge(
                config('database.connections.mysql.options', []),
                [
                    \PDO::ATTR_PERSISTENT => true,
                    \PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                ]
            )]);
        }
    }

    /**
     * Configure le monitoring des performances
     */
    private function configurePerformanceMonitoring(): void
    {
        if (config('performance.monitoring.slow_query_threshold')) {
            DB::listen(function (QueryExecuted $query) {
                $threshold = config('performance.monitoring.slow_query_threshold');
                if ($query->time > $threshold) {
                    Log::channel('performance')->warning('Slow query detected', [
                        'sql' => $query->sql,
                        'time' => $query->time . 'ms',
                        'bindings' => $query->bindings,
                    ]);
                }
            });
        }
    }

    /**
     * Configure le cache des requêtes
     */
    private function configureQueryCache(): void
    {
        if (config('performance.cache.query_cache')) {
            // Macro pour le cache de requêtes
            \Illuminate\Database\Query\Builder::macro('cached', function ($ttl = null) {
                $ttl = $ttl ?? config('performance.database.query_cache_ttl', 3600);
                $key = 'query:' . md5($this->toSql() . serialize($this->getBindings()));
                
                return Cache::remember($key, $ttl, function () {
                    return $this->get();
                });
            });
        }
    }

    /**
     * Configure les optimisations de session
     */
    private function configureSessionOptimizations(): void
    {
        if (app()->environment('production')) {
            // Configuration des cookies sécurisés
            config([
                'session.secure' => config('performance.session.cookie_secure', true),
                'session.http_only' => config('performance.session.cookie_httponly', true),
                'session.same_site' => config('performance.session.cookie_samesite', 'strict'),
            ]);
        }
    }
}
