# 🕐 ClockIn - API de Gestion du Pointage Enterprise

ClockIn est une API Laravel professionnelle optimisée pour l'entreprise, dédiée à la gestion du pointage des employés avec géolocalisation, monitoring avancé et configuration haute performance. Développée pour fonctionner avec une application mobile Flutter.

## 🚀 Optimisations Enterprise

### Performance & Scalabilité
- ✅ **Configuration Redis avancée** - Cache, sessions et queues optimisés
- ✅ **Pool de connexions MySQL** - Gestion optimisée des connexions DB
- ✅ **OPcache avec préchargement** - Performances PHP maximales
- ✅ **Cache multi-niveaux** - Configuration, routes, vues et requêtes
- ✅ **Compression Gzip/Brotli** - Optimisation de la bande passante
- ✅ **CDN Ready** - Headers de cache optimisés

### Sécurité Enterprise
- ✅ **Middleware de sécurité avancé** - Protection SQL injection, XSS
- ✅ **Rate limiting intelligent** - Protection contre les attaques DDoS
- ✅ **Headers de sécurité** - HSTS, CSP, X-Frame-Options
- ✅ **Logging de sécurité** - Audit trail complet
- ✅ **Détection de menaces** - Blocage automatique des IPs suspectes

### Monitoring & Observabilité
- ✅ **Monitoring en temps réel** - Métriques de performance
- ✅ **Health checks** - Surveillance de la santé de l'application
- ✅ **Logs structurés** - Performance, audit, sécurité
- ✅ **Alertes automatiques** - Notification des problèmes
- ✅ **Dashboard de monitoring** - Interface d'administration

### Infrastructure Docker
- ✅ **Configuration Docker professionnelle** - Multi-stage builds
- ✅ **Nginx optimisé** - Configuration haute performance
- ✅ **PHP-FPM tuné** - Pool de processus optimisé
- ✅ **Supervisor** - Gestion des workers et processus
- ✅ **Auto-scaling ready** - Configuration pour Kubernetes

## ✨ Fonctionnalités Métier

- ✅ **Authentification sécurisée** avec tokens personnalisés
- ✅ **Gestion des employés** (CRUD complet)
- ✅ **Gestion des chantiers/sites** avec géolocalisation
- ✅ **Pointage géolocalisé** avec vérification de proximité (rayon de 50m)
- ✅ **Vérification de localisation** en temps réel
- ✅ **Logs de traçabilité** pour toutes les actions
- ✅ **API RESTful** avec réponses JSON standardisées
- ✅ **Support multilingue** (Français/Arabe)
- ✅ **Documentation API** interactive
- ✅ **Validation robuste** avec Form Requests
- ✅ **Middleware de sécurité** pour les rôles admin
- ✅ **Tests unitaires** pour l'authentification
- ✅ **Configuration CORS** pour Flutter

## 📋 Prérequis

- PHP 8.2+
- Composer
- MySQL 8.0+
- WampServer (pour le développement local)

## 🚀 Installation Rapide

### 1. Cloner et installer
```bash
git clone <repository-url>
cd clockin
composer install
```

### 2. Configuration
```bash
cp .env.example .env
php artisan key:generate
```

### 3. Base de données
- Accédez à phpMyAdmin : http://localhost:8080/phpmyadmin/
- Créez la base `clockin_db`
- Exécutez les migrations :
```bash
php artisan migrate
php artisan db:seed
```

### 4. Démarrer le serveur
```bash
php artisan serve --host=127.0.0.1 --port=8000
```

## 📚 Documentation API

**URL de la documentation :** http://127.0.0.1:8000/docs

### 🔐 Authentification

**Login :**
```bash
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

**Comptes de test créés :**
- **Admin :** <EMAIL> / password123
- **Employés :** <EMAIL>, <EMAIL>, <EMAIL> / password123

### 📍 Endpoints Principaux

| Méthode | Endpoint | Description | Auth |
|---------|----------|-------------|------|
| POST | `/api/auth/login` | Connexion | ❌ |
| POST | `/api/auth/logout` | Déconnexion | ✅ |
| GET | `/api/sites` | Liste des sites | ✅ Admin |
| POST | `/api/sites` | Créer un site | ✅ Admin |
| GET | `/api/employees` | Liste des employés | ✅ Admin |
| POST | `/api/employees` | Créer un employé | ✅ Admin |
| POST | `/api/pointage/check-location` | Vérifier proximité | ✅ |
| POST | `/api/pointage/save-pointage` | Enregistrer pointage | ✅ |
| GET | `/api/pointage/pointages` | Liste pointages | ✅ Admin |

## 🗄️ Structure de la Base de Données

### ✅ Structure 100% Conforme à l'Original

La structure de la base de données a été **analysée et corrigée** pour correspondre exactement à la structure originale fournie.

### Tables Créées
- `users` - Utilisateurs (admin/employés) avec rôles
- `sites` - Chantiers/sites de travail avec coordonnées GPS
- `pointages` - Enregistrements de pointage avec géolocalisation
- `verifications` - Vérifications de localisation
- `assignments` - Assignations employé-site (relation N:N)
- `logs` - Logs de traçabilité (structure corrigée : sans `updated_at`)

### Corrections Appliquées
- ✅ **Table `logs`** : Suppression de `updated_at`, conservation uniquement de `created_at`
- ✅ **Table `users`** : Suppression des colonnes non conformes (`email_verified_at`, `remember_token`)
- ✅ **Index nommés** : Tous les index respectent la nomenclature originale
- ✅ **Contraintes** : Clé unique `uk_assignments_user_site` correctement nommée
- ✅ **Types de données** : DECIMAL(10,8) et DECIMAL(11,8) respectés

### Relations
- Users 1:N Pointages
- Users 1:N Verifications
- Users 1:N Logs
- Sites N:N Users (via assignments)
- Sites 1:N Pointages

### Validation
- 📊 **Script de vérification** : `database/check_structure.php`
- 📋 **Rapport détaillé** : `STRUCTURE_ANALYSIS_REPORT.md`

## 🧪 Tests

```bash
# Exécuter tous les tests
php artisan test

# Tests d'authentification
php artisan test --filter AuthTest

# Tests de structure de base de données
php artisan test --filter LogStructureTest
```

**Tests implémentés :**
- ✅ Login avec identifiants valides
- ✅ Login avec identifiants invalides
- ✅ Validation des champs requis
- ✅ Format email invalide
- ✅ Structure de la table logs conforme
- ✅ Création de logs sans `updated_at`
- ✅ Logs automatiques lors des actions API

## 🔧 Architecture du Code

### Controllers Organisés
```
app/Http/Controllers/
├── Auth/AuthController.php          # Authentification
├── Pointage/PointageController.php  # Gestion pointage
├── Site/SiteController.php          # Gestion sites
└── Employee/EmployeeController.php  # Gestion employés
```

### Middleware Personnalisés
- `AdminMiddleware` - Vérification rôle admin
- `SimpleAuthMiddleware` - Authentification par token

### API Resources
- `UserResource` - Formatage utilisateurs
- `SiteResource` - Formatage sites
- `PointageResource` - Formatage pointages

### Form Requests
- `LoginRequest` - Validation login
- `CheckLocationRequest` - Validation géolocalisation

## 📱 Intégration Flutter

### Configuration HTTP Client
```dart
class ApiService {
  static const String baseUrl = 'http://127.0.0.1:8000/api';
  
  static Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (token != null) 'Authorization': 'Bearer $token',
  };
}
```

### Exemple d'utilisation
```dart
// Login
final response = await http.post(
  Uri.parse('$baseUrl/auth/login'),
  headers: headers,
  body: jsonEncode({
    'email': '<EMAIL>',
    'password': 'password123'
  }),
);

// Vérifier localisation
final locationResponse = await http.post(
  Uri.parse('$baseUrl/pointage/check-location'),
  headers: headers,
  body: jsonEncode({
    'site_id': 1,
    'latitude': 33.5731,
    'longitude': -7.5898
  }),
);
```

## 🌍 Support Multilingue

Tous les messages incluent :
- `message` - Message en français
- `message_ar` - Message en arabe

## 🚨 Sécurité Implémentée

- ✅ Validation stricte des entrées
- ✅ Authentification par token
- ✅ Middleware de vérification des rôles
- ✅ Logs de toutes les actions sensibles
- ✅ Hashage sécurisé des mots de passe (bcrypt)
- ✅ Vérification de proximité géographique (50m)
- ✅ Protection CORS configurée

## 📊 Fonctionnalités Avancées

### Géolocalisation
- Calcul de distance avec formule Haversine
- Vérification automatique du rayon de 50m
- Enregistrement des coordonnées de début/fin

### Logs de Traçabilité
- Toutes les actions sont loggées
- Détails des tentatives échouées
- Historique complet des modifications

### Gestion des Erreurs
- Codes HTTP standardisés
- Messages d'erreur clairs
- Validation complète des données

## 🔄 Prochaines Améliorations

- [ ] Intégration Laravel Sanctum complète
- [ ] Notifications push pour vérifications
- [ ] Rapports de pointage avancés
- [ ] Export des données (PDF, Excel)
- [ ] Interface d'administration web

## 📞 Support

- **Documentation API :** http://127.0.0.1:8000/docs
- **Logs Laravel :** `storage/logs/laravel.log`
- **Base de données :** http://localhost:8080/phpmyadmin/

## 📄 Licence

Ce projet est sous licence MIT.

---

**Développé avec ❤️ pour la gestion moderne du pointage des employés**
