{"info": {"_postman_id": "clockin-api-enterprise", "name": "ClockIn API Enterprise", "description": "Collection complète pour tester l'API ClockIn optimisée pour l'entreprise", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "🔐 Authentification", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.token).to.exist;", "    pm.environment.set(\"admin_token\", jsonData.data.token);", "});", "", "pm.test(\"User is admin\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.role).to.eql(\"admin\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.token).to.exist;", "    pm.environment.set(\"employee_token\", jsonData.data.token);", "});", "", "pm.test(\"User is employee\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.user.role).to.eql(\"employee\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"password123\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}}, {"name": "User Info", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"User data is present\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.id).to.exist;", "    pm.expect(jsonData.data.email).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/auth/user", "host": ["{{base_url}}"], "path": ["auth", "user"]}}}, {"name": "Logout", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Success message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}}]}, {"name": "📍 Pointage", "item": [{"name": "Vérifier Localisation", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has location data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.within_range).to.exist;", "    pm.expect(jsonData.distance).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{employee_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/pointage/check-location", "host": ["{{base_url}}"], "path": ["pointage", "check-location"]}}}, {"name": "Enregistrer <PERSON>age", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 201\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test(\"Response has pointage data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.pointage).to.exist;", "    pm.expect(jsonData.pointage.user).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{employee_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 33.5731,\n    \"longitude\": -7.5898\n}"}, "url": {"raw": "{{base_url}}/pointage", "host": ["{{base_url}}"], "path": ["pointage"]}}}, {"name": "Liste Pointages (Admin)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has data array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{admin_token}}"}], "url": {"raw": "{{base_url}}/pointage", "host": ["{{base_url}}"], "path": ["pointage"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:8080/api"}]}