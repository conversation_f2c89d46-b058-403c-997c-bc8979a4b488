{"id": "clockin-environment", "name": "ClockIn Environment", "values": [{"key": "base_url", "value": "http://localhost:8080/api", "description": "URL de base de l'API ClockIn", "enabled": true}, {"key": "admin_token", "value": "", "description": "Token d'authentification admin (rempli automatiquement après login)", "enabled": true}, {"key": "employee_token", "value": "", "description": "Token d'authentification employé (rempli automatiquement après login)", "enabled": true}, {"key": "site_id", "value": "1", "description": "ID du site pour les tests", "enabled": true}, {"key": "user_id", "value": "2", "description": "ID de l'utilisateur employé pour les tests", "enabled": true}, {"key": "test_latitude", "value": "33.5731", "description": "Latitude de test (Casablanca)", "enabled": true}, {"key": "test_longitude", "value": "-7.5898", "description": "Longitude de test (Casablanca)", "enabled": true}, {"key": "admin_email", "value": "<EMAIL>", "description": "Email admin par défaut", "enabled": true}, {"key": "admin_password", "value": "password123", "description": "Mot de passe admin par défaut", "enabled": true}, {"key": "employee_email", "value": "<EMAIL>", "description": "Email employé par défaut", "enabled": true}, {"key": "employee_password", "value": "password123", "description": "Mot de passe employé par défaut", "enabled": true}], "_postman_variable_scope": "environment"}