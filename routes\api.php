<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Pointage\PointageController;
use App\Http\Controllers\Site\SiteController;
use App\Http\Controllers\Verification\VerificationController;
use App\Http\Controllers\Employee\EmployeeController;
use App\Http\Controllers\Monitoring\HealthController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Routes publiques (sans authentification)
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});

// Routes de monitoring (accès public pour health check)
Route::get('/health', [HealthController::class, 'health']);

// Routes protégées par authentification
Route::middleware(['auth:sanctum'])->group(function () {
    
    // Authentification
    Route::prefix('auth')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);
    });

    // Pointage
    Route::prefix('pointage')->group(function () {
        Route::post('/check-location', [PointageController::class, 'checkLocation']);
        Route::post('/', [PointageController::class, 'savePointage']); // Modifié pour correspondre à votre appel
        Route::get('/', [PointageController::class, 'index'])->middleware('admin');
        Route::get('/export', [PointageController::class, 'export'])->middleware('admin');
    });

    // Vérification de localisation
    Route::prefix('verification')->group(function () {
        Route::post('/request', [VerificationController::class, 'requestVerification'])->middleware('admin');
        Route::post('/', [PointageController::class, 'verifyLocation']);
    });

    // Gestion des chantiers (Admin seulement)
    Route::middleware('admin')->prefix('sites')->group(function () {
        Route::get('/', [SiteController::class, 'index']);
        Route::post('/', [SiteController::class, 'store']);
        Route::get('/{site}', [SiteController::class, 'show']);
        Route::put('/{site}', [SiteController::class, 'update']);
        Route::delete('/{site}', [SiteController::class, 'destroy']);
        Route::post('/assign', [SiteController::class, 'assignSite']);
    });

    // Gestion des employés (Admin seulement)
    Route::middleware('admin')->prefix('employees')->group(function () {
        Route::get('/', [EmployeeController::class, 'index']);
        Route::post('/', [EmployeeController::class, 'store']);
        Route::get('/{user}', [EmployeeController::class, 'show']);
        Route::put('/{user}', [EmployeeController::class, 'update']);
        Route::delete('/{user}', [EmployeeController::class, 'destroy']);
    });

    // Monitoring et métriques (Admin seulement)
    Route::middleware('admin')->prefix('monitoring')->group(function () {
        Route::get('/metrics', [HealthController::class, 'metrics']);
        Route::get('/status', [HealthController::class, 'status']);
        Route::get('/dashboard', [HealthController::class, 'dashboard']);
    });
});