<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'simple.auth' => \App\Http\Middleware\SimpleAuthMiddleware::class,
            'performance' => \App\Http\Middleware\PerformanceMiddleware::class,
        ]);

        // Ajouter le middleware de performance globalement en production
        if (app()->environment('production')) {
            $middleware->append(\App\Http\Middleware\PerformanceMiddleware::class);
        }
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
