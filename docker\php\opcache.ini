[opcache]
; Configuration OPcache optimisée pour l'entreprise

; Activer OPcache
opcache.enable = 1
opcache.enable_cli = 1

; Configuration de la mémoire
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000

; Configuration de la validation
opcache.validate_timestamps = 0
opcache.revalidate_freq = 0
opcache.save_comments = 0
opcache.enable_file_override = 1

; Configuration des optimisations
opcache.optimization_level = 0x7FFEBFFF
opcache.max_file_size = 0
opcache.consistency_checks = 0
opcache.force_restart_timeout = 180

; Configuration JIT (Just In Time compilation)
opcache.jit = tracing
opcache.jit_buffer_size = 128M
opcache.jit_debug = 0

; Configuration des logs
opcache.error_log = /var/log/opcache_errors.log
opcache.log_verbosity_level = 2

; Configuration de la précharge (preloading)
opcache.preload = /var/www/html/config/opcache-preload.php
opcache.preload_user = www-data

; Configuration de la protection
opcache.protect_memory = 1
opcache.restrict_api = ""

; Configuration du cache de fichiers
opcache.file_cache = /tmp/opcache
opcache.file_cache_only = 0
opcache.file_cache_consistency_checks = 1

; Configuration de la compilation
opcache.fast_shutdown = 1
opcache.enable_file_override = 1

; Configuration de la validation des timestamps
opcache.validate_permission = 0
opcache.validate_root = 0

; Configuration des statistiques
opcache.record_warnings = 1
opcache.log_verbosity_level = 2
