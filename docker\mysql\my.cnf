[mysqld]
# Configuration MySQL optimisée pour l'entreprise

# Configuration de base
user = mysql
default-storage-engine = InnoDB
socket = /var/lib/mysql/mysql.sock
pid-file = /var/lib/mysql/mysql.pid

# Configuration des connexions
bind-address = 0.0.0.0
port = 3306
max_connections = 200
max_connect_errors = 1000000
max_user_connections = 100

# Configuration des buffers et cache
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Configuration du cache de requêtes
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 2M

# Configuration des tables temporaires
tmp_table_size = 256M
max_heap_table_size = 256M

# Configuration des threads
thread_cache_size = 50
thread_stack = 256K

# Configuration des timeouts
wait_timeout = 28800
interactive_timeout = 28800
connect_timeout = 60

# Configuration de la sécurité
sql_mode = STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION
local_infile = 0

# Configuration des logs
log_error = /var/log/mysql/error.log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# Configuration du binlog
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Configuration InnoDB
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_thread_concurrency = 0
innodb_lock_wait_timeout = 120
innodb_deadlock_detect = 1

# Configuration des caractères
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# Configuration de performance
key_buffer_size = 256M
read_buffer_size = 2M
read_rnd_buffer_size = 16M
sort_buffer_size = 8M
join_buffer_size = 8M

# Configuration des tables MyISAM (si utilisées)
myisam_sort_buffer_size = 128M
myisam_max_sort_file_size = 10G
myisam_repair_threads = 1

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/lib/mysql/mysql.sock

[mysqldump]
quick
quote-names
max_allowed_packet = 1024M

[mysqlhotcopy]
interactive-timeout
