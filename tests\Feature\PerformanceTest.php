<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Site;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class PerformanceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur admin pour les tests
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Créer un utilisateur employé
        $this->employee = User::factory()->create([
            'role' => 'employee',
            'email' => '<EMAIL>',
        ]);
    }

    /**
     * Test que les APIs principales répondent dans un délai acceptable
     */
    public function test_api_response_times(): void
    {
        $maxResponseTime = 2000; // 2 secondes en millisecondes

        // Test de l'endpoint de login
        $start = microtime(true);
        $response = $this->postJson('/api/auth/login', [
            'email' => $this->admin->email,
            'password' => 'password',
        ]);
        $loginTime = (microtime(true) - $start) * 1000;

        $response->assertStatus(200);
        $this->assertLessThan($maxResponseTime, $loginTime, "Login endpoint too slow: {$loginTime}ms");

        // Récupérer le token pour les tests suivants
        $token = $response->json('data.token');

        // Test de l'endpoint des sites
        $start = microtime(true);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/sites');
        $sitesTime = (microtime(true) - $start) * 1000;

        $response->assertStatus(200);
        $this->assertLessThan($maxResponseTime, $sitesTime, "Sites endpoint too slow: {$sitesTime}ms");

        // Test de l'endpoint des employés
        $start = microtime(true);
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/employees');
        $employeesTime = (microtime(true) - $start) * 1000;

        $response->assertStatus(200);
        $this->assertLessThan($maxResponseTime, $employeesTime, "Employees endpoint too slow: {$employeesTime}ms");
    }

    /**
     * Test que le cache fonctionne correctement
     */
    public function test_cache_functionality(): void
    {
        $key = 'test_cache_key';
        $value = 'test_cache_value';
        $ttl = 60; // 1 minute

        // Test de mise en cache
        Cache::put($key, $value, $ttl);
        $this->assertTrue(Cache::has($key));
        $this->assertEquals($value, Cache::get($key));

        // Test de suppression du cache
        Cache::forget($key);
        $this->assertFalse(Cache::has($key));
    }

    /**
     * Test que la base de données répond rapidement
     */
    public function test_database_performance(): void
    {
        $maxQueryTime = 100; // 100ms

        // Test d'une requête simple
        $start = microtime(true);
        DB::select('SELECT 1');
        $queryTime = (microtime(true) - $start) * 1000;

        $this->assertLessThan($maxQueryTime, $queryTime, "Simple query too slow: {$queryTime}ms");

        // Test d'une requête plus complexe avec des données
        Site::factory()->count(10)->create();
        
        $start = microtime(true);
        $sites = Site::with('users')->get();
        $complexQueryTime = (microtime(true) - $start) * 1000;

        $this->assertLessThan($maxQueryTime * 5, $complexQueryTime, "Complex query too slow: {$complexQueryTime}ms");
    }

    /**
     * Test de charge sur l'endpoint de pointage
     */
    public function test_pointage_endpoint_load(): void
    {
        // Créer un site et assigner l'employé
        $site = Site::factory()->create([
            'latitude' => 33.5731,
            'longitude' => -7.5898,
        ]);

        DB::table('assignments')->insert([
            'user_id' => $this->employee->id,
            'site_id' => $site->id,
        ]);

        // Simuler un token pour l'employé
        $token = base64_encode($this->employee->id . '|' . time() . '|' . $this->employee->email);

        $maxResponseTime = 1000; // 1 seconde
        $iterations = 10;
        $totalTime = 0;

        for ($i = 0; $i < $iterations; $i++) {
            $start = microtime(true);
            
            $response = $this->withHeaders([
                'Authorization' => 'Bearer ' . $token,
            ])->postJson('/api/pointage/check-location', [
                'latitude' => 33.5731,
                'longitude' => -7.5898,
            ]);
            
            $responseTime = (microtime(true) - $start) * 1000;
            $totalTime += $responseTime;

            $response->assertStatus(200);
            $this->assertLessThan($maxResponseTime, $responseTime, "Pointage check too slow on iteration {$i}: {$responseTime}ms");
        }

        $averageTime = $totalTime / $iterations;
        $this->assertLessThan($maxResponseTime / 2, $averageTime, "Average pointage check time too slow: {$averageTime}ms");
    }

    /**
     * Test de l'endpoint de health check
     */
    public function test_health_check_endpoint(): void
    {
        $response = $this->getJson('/api/health');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'timestamp',
                    'checks' => [
                        'database',
                        'redis',
                        'disk',
                        'memory',
                    ],
                ]);

        $health = $response->json();
        $this->assertContains($health['status'], ['healthy', 'warning', 'unhealthy']);
    }

    /**
     * Test de l'endpoint de métriques (admin seulement)
     */
    public function test_metrics_endpoint(): void
    {
        // Simuler un token admin
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/monitoring/metrics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'timestamp',
                    'global',
                    'database',
                    'redis',
                ]);
    }

    /**
     * Test que les middlewares de sécurité fonctionnent
     */
    public function test_security_middleware(): void
    {
        // Test de tentative d'injection SQL
        $response = $this->postJson('/api/auth/login', [
            'email' => "<EMAIL>' OR '1'='1",
            'password' => 'password',
        ]);

        // L'endpoint devrait toujours répondre (pas de crash)
        $this->assertContains($response->status(), [401, 422, 403]);

        // Test de tentative XSS
        $response = $this->postJson('/api/auth/login', [
            'email' => '<script>alert("xss")</script>',
            'password' => 'password',
        ]);

        $this->assertContains($response->status(), [401, 422, 403]);
    }

    /**
     * Test de performance de l'autoloader optimisé
     */
    public function test_autoloader_performance(): void
    {
        $maxLoadTime = 50; // 50ms

        // Test de chargement de plusieurs classes
        $classes = [
            \App\Models\User::class,
            \App\Models\Site::class,
            \App\Models\Pointage::class,
            \App\Http\Controllers\Auth\AuthController::class,
            \App\Http\Controllers\Pointage\PointageController::class,
        ];

        foreach ($classes as $class) {
            $start = microtime(true);
            $reflection = new \ReflectionClass($class);
            $loadTime = (microtime(true) - $start) * 1000;

            $this->assertLessThan($maxLoadTime, $loadTime, "Class {$class} loading too slow: {$loadTime}ms");
        }
    }

    /**
     * Test de la mémoire utilisée
     */
    public function test_memory_usage(): void
    {
        $initialMemory = memory_get_usage(true);
        
        // Effectuer quelques opérations
        $users = User::factory()->count(100)->create();
        $sites = Site::factory()->count(50)->create();
        
        $finalMemory = memory_get_usage(true);
        $memoryUsed = $finalMemory - $initialMemory;
        
        // Vérifier que l'utilisation mémoire reste raisonnable (moins de 50MB)
        $maxMemoryUsage = 50 * 1024 * 1024; // 50MB
        $this->assertLessThan($maxMemoryUsage, $memoryUsed, "Memory usage too high: " . ($memoryUsed / 1024 / 1024) . "MB");
    }

    /**
     * Test de stress sur les APIs
     */
    public function test_api_stress(): void
    {
        $token = base64_encode($this->admin->id . '|' . time() . '|' . $this->admin->email);
        
        $iterations = 50;
        $maxFailures = 5; // Maximum 10% de taux d'échec
        $failures = 0;

        for ($i = 0; $i < $iterations; $i++) {
            try {
                $response = $this->withHeaders([
                    'Authorization' => 'Bearer ' . $token,
                ])->getJson('/api/auth/user');

                if ($response->status() !== 200) {
                    $failures++;
                }
            } catch (\Exception $e) {
                $failures++;
            }
        }

        $failureRate = ($failures / $iterations) * 100;
        $this->assertLessThan(10, $failureRate, "API failure rate too high: {$failureRate}%");
    }
}
