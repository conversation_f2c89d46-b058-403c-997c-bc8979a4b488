<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\PerformanceMonitor;
use Illuminate\Support\Facades\Log;

class PerformanceMiddleware
{
    private PerformanceMonitor $monitor;

    public function __construct(PerformanceMonitor $monitor)
    {
        $this->monitor = $monitor;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Démarrer le monitoring
        $operationName = $this->getOperationName($request);
        $this->monitor->start($operationName);

        // Traiter la requête
        $response = $next($request);

        // Terminer le monitoring
        $metrics = $this->monitor->end($operationName);
        
        // Ajouter les métriques globales
        $globalMetrics = $this->monitor->getGlobalMetrics();
        $metrics = array_merge($metrics, [
            'global' => $globalMetrics,
            'request' => [
                'method' => $request->method(),
                'url' => $request->fullUrl(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => $request->user()?->id,
            ],
            'response' => [
                'status_code' => $response->getStatusCode(),
                'content_length' => strlen($response->getContent()),
            ],
        ]);

        // Vérifier les alertes
        $alerts = $this->monitor->checkAlerts($globalMetrics);
        if (!empty($alerts)) {
            $metrics['alerts'] = $alerts;
            Log::channel('performance')->warning('Performance alerts triggered', $alerts);
        }

        // Enregistrer les métriques
        $this->monitor->logMetrics($metrics);

        // Ajouter les headers de performance en mode debug
        if (config('app.debug')) {
            $response->headers->set('X-Response-Time', $metrics['duration'] . 'ms');
            $response->headers->set('X-Memory-Usage', $this->formatBytes($metrics['memory_used']));
            $response->headers->set('X-Memory-Peak', $this->formatBytes($globalMetrics['memory_peak']));
            $response->headers->set('X-Queries-Count', $globalMetrics['queries_count']);
        }

        return $response;
    }

    /**
     * Génère un nom d'opération basé sur la requête
     */
    private function getOperationName(Request $request): string
    {
        $route = $request->route();
        if ($route) {
            $action = $route->getActionName();
            if (str_contains($action, '@')) {
                return str_replace(['App\\Http\\Controllers\\', '@'], ['', '.'], $action);
            }
            return $route->getName() ?: $request->method() . ' ' . $request->path();
        }

        return $request->method() . ' ' . $request->path();
    }

    /**
     * Formate les bytes en format lisible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
