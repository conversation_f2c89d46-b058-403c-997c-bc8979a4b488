[www]
; Configuration PHP-FPM optimisée pour l'entreprise

; Utilisateur et groupe
user = www-data
group = www-data

; Configuration du socket
listen = 9000
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Configuration des processus
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 1000

; Configuration des timeouts
request_terminate_timeout = 300s
request_slowlog_timeout = 10s

; Configuration des logs
access.log = /var/log/php-fpm-access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"
slowlog = /var/log/php-fpm-slow.log

; Configuration de la sécurité
security.limit_extensions = .php

; Configuration des variables d'environnement
clear_env = no
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; Configuration du monitoring
pm.status_path = /status
ping.path = /ping
ping.response = pong

; Configuration des limites de ressources
rlimit_files = 65536
rlimit_core = 0

; Configuration de la priorité des processus
process.priority = -10

; Configuration du catch des signaux
catch_workers_output = yes
decorate_workers_output = no

; Configuration de la compression
php_admin_value[zlib.output_compression] = On
php_admin_value[zlib.output_compression_level] = 6

; Configuration de la mémoire
php_admin_value[memory_limit] = 512M

; Configuration des sessions
php_admin_value[session.save_handler] = redis
php_admin_value[session.save_path] = "tcp://redis:6379?database=2"

; Configuration des logs d'erreurs
php_admin_value[error_log] = /var/log/php-errors.log
php_admin_flag[log_errors] = on

; Configuration de l'upload
php_admin_value[upload_max_filesize] = 100M
php_admin_value[post_max_size] = 100M

; Configuration de l'exécution
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300
