version: '3.8'

services:
  # Application PHP-FPM
  app:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      target: production
    container_name: clockin_app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - ./docker/php/opcache.ini:/usr/local/etc/php/conf.d/opcache.ini
      - ./storage/logs:/var/www/html/storage/logs
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=app
    depends_on:
      - mysql
      - redis
    networks:
      - clockin_network

  # Nginx Web Server
  nginx:
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
    container_name: clockin_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - .:/var/www/html
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/sites-available:/etc/nginx/sites-available
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./storage/logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - clockin_network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: clockin_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - clockin_network

  # Redis Cache & Sessions
  redis:
    image: redis:7-alpine
    container_name: clockin_redis
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      - clockin_network

  # Queue Worker
  queue:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      target: production
    container_name: clockin_queue
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=queue
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    depends_on:
      - mysql
      - redis
    networks:
      - clockin_network

  # Scheduler
  scheduler:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
      target: production
    container_name: clockin_scheduler
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
    environment:
      - APP_ENV=production
      - CONTAINER_ROLE=scheduler
    command: php artisan schedule:work
    depends_on:
      - mysql
      - redis
    networks:
      - clockin_network

  # Supervisor pour la gestion des processus
  supervisor:
    build:
      context: .
      dockerfile: docker/supervisor/Dockerfile
    container_name: clockin_supervisor
    restart: unless-stopped
    volumes:
      - .:/var/www/html
      - ./docker/supervisor/supervisord.conf:/etc/supervisor/conf.d/supervisord.conf
      - ./storage/logs/supervisor:/var/log/supervisor
    depends_on:
      - app
      - redis
    networks:
      - clockin_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  clockin_network:
    driver: bridge
