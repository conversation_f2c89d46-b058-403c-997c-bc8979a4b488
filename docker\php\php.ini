[PHP]
; Configuration PHP optimisée pour la production

; Gestion des erreurs
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT

; Limites de ressources
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
max_input_vars = 3000
post_max_size = 100M
upload_max_filesize = 100M

; Configuration des sessions
session.save_handler = redis
session.save_path = "tcp://redis:6379?database=2"
session.gc_probability = 1
session.gc_divisor = 1000
session.gc_maxlifetime = 7200
session.cookie_secure = 1
session.cookie_httponly = 1
session.cookie_samesite = "Strict"
session.use_strict_mode = 1

; Optimisations de performance
realpath_cache_size = 4096K
realpath_cache_ttl = 600

; Configuration de date
date.timezone = "UTC"

; Configuration de sécurité
expose_php = Off
allow_url_fopen = Off
allow_url_include = Off

; Configuration des logs
log_errors_max_len = 1024

; Configuration de l'output buffering
output_buffering = 4096
implicit_flush = Off

; Configuration des variables
variables_order = "GPCS"
request_order = "GP"

; Configuration des fichiers
file_uploads = On
upload_tmp_dir = /tmp

; Configuration de la compression
zlib.output_compression = On
zlib.output_compression_level = 6

; Configuration de l'autoloader
auto_prepend_file =
auto_append_file =

; Configuration des assertions (désactivées en production)
assert.active = Off
assert.exception = Off

; Configuration de la validation des données
filter.default = unsafe_raw
filter.default_flags =
