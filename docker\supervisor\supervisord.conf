[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
loglevel=info

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

# Interface web Supervisor (optionnel, pour le monitoring)
[inet_http_server]
port=*:9001
username=admin
password=clockin_supervisor

# Worker de queue par défaut
[program:queue-default]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --queue=default --sleep=3 --tries=3 --max-time=3600 --memory=512
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue-default.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=60
stopsignal=TERM

# Worker de queue haute priorité
[program:queue-high]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --queue=high --sleep=1 --tries=5 --max-time=1800 --memory=256
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue-high.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=30
stopsignal=TERM
priority=100

# Worker de queue pour les tâches de fond
[program:queue-background]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --queue=background --sleep=5 --tries=2 --max-time=7200 --memory=1024
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/queue-background.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=120
stopsignal=TERM
priority=200

# Scheduler Laravel
[program:scheduler]
process_name=%(program_name)s
command=php /var/www/html/artisan schedule:work
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/scheduler.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=60
stopsignal=TERM

# Monitoring des performances
[program:performance-monitor]
process_name=%(program_name)s
command=php /var/www/html/artisan performance:monitor
directory=/var/www/html
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/performance-monitor.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stopwaitsecs=30
stopsignal=TERM

# Nettoyage des logs (exécuté périodiquement)
[program:log-cleaner]
process_name=%(program_name)s
command=php /var/www/html/artisan logs:clean
directory=/var/www/html
autostart=false
autorestart=false
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/var/log/supervisor/log-cleaner.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=3

# Groupe pour tous les workers de queue
[group:queues]
programs=queue-default,queue-high,queue-background
priority=999

# Configuration des événements
[eventlistener:memmon]
command=memmon -a 400MB -m <EMAIL>
events=PROCESS_STATE_RUNNING
buffer_size=10
directory=/var/www/html
user=www-data
