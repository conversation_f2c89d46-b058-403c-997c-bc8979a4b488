#!/bin/bash

# Script de déploiement et d'optimisation pour l'environnement de production
# ClockIn Application - Enterprise Grade Configuration

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction de logging
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "artisan" ]; then
    error "Ce script doit être exécuté depuis la racine du projet Laravel"
fi

log "🚀 Début du déploiement en production de ClockIn"

# 1. Vérifier les prérequis
log "📋 Vérification des prérequis..."

# Vérifier PHP
if ! command -v php &> /dev/null; then
    error "PHP n'est pas installé"
fi

PHP_VERSION=$(php -r "echo PHP_VERSION;")
info "Version PHP: $PHP_VERSION"

# Vérifier Composer
if ! command -v composer &> /dev/null; then
    error "Composer n'est pas installé"
fi

# Vérifier Node.js (pour les assets)
if ! command -v node &> /dev/null; then
    warn "Node.js n'est pas installé - les assets ne seront pas compilés"
fi

# 2. Mettre l'application en mode maintenance
log "🔧 Activation du mode maintenance..."
php artisan down --message="Mise à jour en cours..." --retry=60

# 3. Sauvegarder la base de données
log "💾 Sauvegarde de la base de données..."
BACKUP_DIR="storage/backups"
mkdir -p $BACKUP_DIR
BACKUP_FILE="$BACKUP_DIR/backup_$(date +%Y%m%d_%H%M%S).sql"

if command -v mysqldump &> /dev/null; then
    mysqldump -h ${DB_HOST:-127.0.0.1} -u ${DB_USERNAME:-root} -p${DB_PASSWORD} ${DB_DATABASE:-clockin_db} > $BACKUP_FILE
    log "✅ Sauvegarde créée: $BACKUP_FILE"
else
    warn "mysqldump non disponible - sauvegarde manuelle recommandée"
fi

# 4. Installer les dépendances
log "📦 Installation des dépendances Composer..."
composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist

# 5. Installer les dépendances Node.js et compiler les assets
if command -v npm &> /dev/null; then
    log "🎨 Compilation des assets..."
    npm ci --only=production
    npm run build
fi

# 6. Optimisations Laravel
log "⚡ Optimisations Laravel..."

# Nettoyer les caches existants
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Créer les nouveaux caches
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache

# Optimiser l'autoloader
composer dump-autoload --optimize --classmap-authoritative

log "✅ Optimisations Laravel terminées"

# 7. Exécuter les migrations
log "🗄️ Exécution des migrations..."
php artisan migrate --force

# 8. Créer les liens symboliques
log "🔗 Création des liens symboliques..."
php artisan storage:link

# 9. Définir les permissions
log "🔐 Configuration des permissions..."
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# 10. Optimisations de performance spécifiques
log "🚀 Optimisations de performance..."

# Vérifier et configurer OPcache
if php -m | grep -q "Zend OPcache"; then
    info "OPcache détecté et activé"
else
    warn "OPcache non détecté - performance réduite"
fi

# Vérifier Redis
if php -m | grep -q "redis"; then
    info "Extension Redis détectée"
    # Tester la connexion Redis
    if php artisan tinker --execute="Cache::store('redis')->put('test', 'ok', 10); echo Cache::store('redis')->get('test');" | grep -q "ok"; then
        log "✅ Connexion Redis fonctionnelle"
    else
        warn "Problème de connexion Redis"
    fi
else
    warn "Extension Redis non détectée"
fi

# 11. Tests de santé
log "🏥 Tests de santé de l'application..."

# Test de base de données
if php artisan tinker --execute="DB::select('SELECT 1');" &> /dev/null; then
    log "✅ Base de données accessible"
else
    error "❌ Problème de connexion à la base de données"
fi

# Test des routes principales
if php artisan route:list | grep -q "api/health"; then
    log "✅ Routes API configurées"
else
    warn "Routes API non trouvées"
fi

# 12. Configuration de la surveillance
log "📊 Configuration de la surveillance..."

# Créer les répertoires de logs
mkdir -p storage/logs/supervisor
mkdir -p storage/logs/nginx
mkdir -p storage/logs/performance

# Configurer la rotation des logs
if command -v logrotate &> /dev/null; then
    info "Logrotate disponible pour la rotation des logs"
else
    warn "Logrotate non disponible - rotation manuelle nécessaire"
fi

# 13. Démarrer les services en arrière-plan
log "🔄 Démarrage des services..."

# Redémarrer les workers de queue si Supervisor est disponible
if command -v supervisorctl &> /dev/null; then
    supervisorctl restart all
    log "✅ Workers de queue redémarrés"
else
    warn "Supervisor non disponible - démarrage manuel des workers nécessaire"
fi

# 14. Tests finaux
log "🧪 Tests finaux..."

# Test de performance basique
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost/api/health || echo "0")
if (( $(echo "$RESPONSE_TIME < 2.0" | bc -l) )); then
    log "✅ Test de performance OK (${RESPONSE_TIME}s)"
else
    warn "⚠️ Temps de réponse élevé: ${RESPONSE_TIME}s"
fi

# 15. Sortir du mode maintenance
log "🟢 Désactivation du mode maintenance..."
php artisan up

# 16. Nettoyage final
log "🧹 Nettoyage final..."

# Supprimer les anciens logs (plus de 30 jours)
find storage/logs -name "*.log" -mtime +30 -delete 2>/dev/null || true

# Supprimer les anciennes sauvegardes (plus de 7 jours)
find storage/backups -name "*.sql" -mtime +7 -delete 2>/dev/null || true

# 17. Résumé du déploiement
log "📋 Résumé du déploiement:"
info "- Application: $(php artisan --version)"
info "- Environnement: $(php artisan env)"
info "- Cache: Activé (Config, Routes, Vues)"
info "- OPcache: $(php -m | grep -q 'Zend OPcache' && echo 'Activé' || echo 'Désactivé')"
info "- Redis: $(php -m | grep -q 'redis' && echo 'Activé' || echo 'Désactivé')"
info "- Base de données: Connectée"

log "🎉 Déploiement terminé avec succès!"
log "🌐 L'application ClockIn est maintenant en ligne et optimisée pour la production"

# Afficher les prochaines étapes
info "📝 Prochaines étapes recommandées:"
info "1. Vérifier les logs: tail -f storage/logs/laravel.log"
info "2. Monitorer les performances: /api/monitoring/dashboard"
info "3. Vérifier la santé: /api/health"
info "4. Configurer la surveillance externe (Pingdom, New Relic, etc.)"

exit 0
