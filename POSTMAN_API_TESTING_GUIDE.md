# 📡 Guide de Test des APIs ClockIn avec Postman

## 🚀 Configuration Initiale

### Base URL
```
http://localhost:8080/api
```
ou
```
https://clockin.local/api
```

### Headers Globaux
```
Content-Type: application/json
Accept: application/json
```

---

## 🔐 1. AUTHENTIFICATION

### 1.1 Login (Connexion)
**Endpoint:** `POST /auth/login`

**Headers:**
```
Content-Type: application/json
Accept: application/json
```

**Body (JSON):**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Réponse Succès (200):**
```json
{
    "success": true,
    "message": "Connexion réussie.",
    "message_ar": "تم تسجيل الدخول بنجاح.",
    "data": {
        "user": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>",
            "role": "admin",
            "created_at": "2025-01-15 10:00:00"
        },
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
    }
}
```

**⚠️ Important:** Copiez le `token` pour les requêtes suivantes !

### 1.2 Logout (Déconnexion)
**Endpoint:** `POST /auth/logout`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

**Body:** Aucun

**Réponse Succès (200):**
```json
{
    "success": true,
    "message": "Déconnexion réussie.",
    "message_ar": "تم تسجيل الخروج بنجاح."
}
```

### 1.3 Informations Utilisateur
**Endpoint:** `GET /auth/user`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

**Réponse Succès (200):**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>",
        "role": "admin",
        "created_at": "2025-01-15 10:00:00"
    }
}
```

---

## 📍 2. POINTAGE

### 2.1 Vérifier la Localisation
**Endpoint:** `POST /pointage/check-location`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

**Réponse Succès (200):**
```json
{
    "success": true,
    "within_range": true,
    "distance": 15.25,
    "site": "Chantier Centre-ville",
    "can_pointe": true
}
```

### 2.2 Enregistrer un Pointage
**Endpoint:** `POST /pointage`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

**Réponse Début de Pointage (201):**
```json
{
    "success": true,
    "message": "Début du pointage enregistré.",
    "message_ar": "تم تسجيل بداية الحضور.",
    "pointage": {
        "id": 1,
        "user": {
            "id": 2,
            "name": "Employee Name",
            "email": "<EMAIL>",
            "role": "employee"
        },
        "site": {
            "id": 1,
            "name": "Chantier Centre-ville",
            "latitude": 33.5731,
            "longitude": -7.5898
        },
        "debut_pointage": "2025-01-15 08:00:00",
        "fin_pointage": null,
        "duree": null,
        "debut_latitude": 33.5731,
        "debut_longitude": -7.5898,
        "is_active": true
    }
}
```

### 2.3 Liste des Pointages (Admin)
**Endpoint:** `GET /pointage`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Query Parameters (optionnels):**
```
?page=1&per_page=15&user_id=2&date_from=2025-01-01&date_to=2025-01-31
```

---

## 🏗️ 3. GESTION DES SITES (Admin uniquement)

### 3.1 Liste des Sites
**Endpoint:** `GET /sites`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Query Parameters (optionnels):**
```
?page=1&per_page=15&search=Centre-ville
```

**Réponse Succès (200):**
```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "Chantier Centre-ville",
                "latitude": 33.5731,
                "longitude": -7.5898,
                "users": [],
                "pointages_count": 5,
                "created_at": "2025-01-15 08:00:00"
            }
        ],
        "current_page": 1,
        "total": 1,
        "per_page": 15
    }
}
```

### 3.2 Créer un Site
**Endpoint:** `POST /sites`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "name": "Nouveau Chantier",
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

### 3.3 Détails d'un Site
**Endpoint:** `GET /sites/{id}`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Exemple:** `GET /sites/1`

### 3.4 Modifier un Site
**Endpoint:** `PUT /sites/{id}`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "name": "Chantier Modifié",
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

### 3.5 Supprimer un Site
**Endpoint:** `DELETE /sites/{id}`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

### 3.6 Assigner un Site à un Employé
**Endpoint:** `POST /sites/assign`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "user_id": 2,
    "site_id": 1
}
```

---

## 👥 4. GESTION DES EMPLOYÉS (Admin uniquement)

### 4.1 Liste des Employés
**Endpoint:** `GET /employees`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

### 4.2 Créer un Employé
**Endpoint:** `POST /employees`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "name": "Nouvel Employé",
    "email": "<EMAIL>",
    "password": "password123",
    "role": "employee"
}
```

### 4.3 Détails d'un Employé
**Endpoint:** `GET /employees/{id}`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

### 4.4 Modifier un Employé
**Endpoint:** `PUT /employees/{id}`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "name": "Employé Modifié",
    "email": "<EMAIL>",
    "role": "employee"
}
```

### 4.5 Supprimer un Employé
**Endpoint:** `DELETE /employees/{id}`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

---

## 🏥 5. MONITORING ET SANTÉ

### 5.1 Health Check (Public)
**Endpoint:** `GET /health`

**Headers:**
```
Accept: application/json
```

**Réponse Succès (200):**
```json
{
    "status": "healthy",
    "timestamp": "2025-01-15T10:30:00Z",
    "checks": {
        "database": {
            "status": "healthy",
            "response_time": "5.2ms"
        },
        "redis": {
            "status": "healthy",
            "response_time": "1.1ms"
        },
        "disk": {
            "status": "healthy",
            "usage": "45%"
        },
        "memory": {
            "status": "healthy",
            "usage": "67%"
        }
    }
}
```

### 5.2 Métriques de Performance (Admin)
**Endpoint:** `GET /monitoring/metrics`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

### 5.3 Statut Système (Admin)
**Endpoint:** `GET /monitoring/status`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

### 5.4 Dashboard de Monitoring (Admin)
**Endpoint:** `GET /monitoring/dashboard`

**Headers:**
```
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

---

## 🔍 6. VÉRIFICATION DE LOCALISATION

### 6.1 Demande de Vérification (Admin)
**Endpoint:** `POST /verification/request`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_ADMIN_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "user_id": 2
}
```

### 6.2 Vérifier la Localisation
**Endpoint:** `POST /verification`

**Headers:**
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer YOUR_TOKEN_HERE
```

**Body (JSON):**
```json
{
    "latitude": 33.5731,
    "longitude": -7.5898
}
```

---

## 🧪 7. COLLECTION POSTMAN

### 7.1 Variables d'Environnement
Créez un environnement Postman avec ces variables :

```
base_url: http://localhost:8080/api
admin_token: (à remplir après login admin)
employee_token: (à remplir après login employé)
site_id: 1
user_id: 2
```

### 7.2 Scripts de Test Automatiques

#### Script Pre-request pour l'authentification :
```javascript
// Pour les requêtes nécessitant un token admin
if (pm.request.url.path.includes('sites') ||
    pm.request.url.path.includes('employees') ||
    pm.request.url.path.includes('monitoring')) {
    pm.request.headers.add({
        key: 'Authorization',
        value: 'Bearer ' + pm.environment.get('admin_token')
    });
}

// Pour les requêtes de pointage
if (pm.request.url.path.includes('pointage')) {
    pm.request.headers.add({
        key: 'Authorization',
        value: 'Bearer ' + pm.environment.get('employee_token')
    });
}
```

#### Script de Test pour Login :
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Response has token", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data.token).to.exist;

    // Sauvegarder le token
    if (jsonData.data.user.role === 'admin') {
        pm.environment.set("admin_token", jsonData.data.token);
    } else {
        pm.environment.set("employee_token", jsonData.data.token);
    }
});

pm.test("User data is present", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.data.user.id).to.exist;
    pm.expect(jsonData.data.user.email).to.exist;
    pm.expect(jsonData.data.user.role).to.exist;
});
```

#### Script de Test Générique :
```javascript
pm.test("Status code is successful", function () {
    pm.expect(pm.response.code).to.be.oneOf([200, 201]);
});

pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

pm.test("Response has success field", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.success).to.exist;
});
```

---

## 🔧 8. CODES D'ERREUR COURANTS

### 401 - Non Autorisé
```json
{
    "success": false,
    "message": "Token d'authentification requis.",
    "message_ar": "رمز المصادقة مطلوب."
}
```

### 403 - Accès Refusé
```json
{
    "success": false,
    "message": "Accès refusé. Droits administrateur requis.",
    "message_ar": "تم رفض الوصول. مطلوب صلاحيات المدير."
}
```

### 422 - Erreur de Validation
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "email": ["L'adresse email est requise."],
        "password": ["Le mot de passe est requis."]
    }
}
```

### 429 - Trop de Requêtes
```json
{
    "message": "Too Many Attempts."
}
```

### 500 - Erreur Serveur
```json
{
    "success": false,
    "message": "Erreur interne du serveur.",
    "message_ar": "خطأ داخلي في الخادم."
}
```

---

## 📋 9. SCÉNARIOS DE TEST COMPLETS

### Scénario 1: Workflow Employé Complet
1. **Login Employé** → `POST /auth/login`
2. **Vérifier Localisation** → `POST /pointage/check-location`
3. **Début Pointage** → `POST /pointage`
4. **Fin Pointage** → `POST /pointage` (même endpoint)
5. **Logout** → `POST /auth/logout`

### Scénario 2: Workflow Admin Complet
1. **Login Admin** → `POST /auth/login`
2. **Créer Site** → `POST /sites`
3. **Créer Employé** → `POST /employees`
4. **Assigner Site** → `POST /sites/assign`
5. **Voir Pointages** → `GET /pointage`
6. **Monitoring** → `GET /monitoring/dashboard`

### Scénario 3: Test de Performance
1. **Health Check** → `GET /health`
2. **Métriques** → `GET /monitoring/metrics`
3. **Statut Système** → `GET /monitoring/status`

---

## 🚀 10. IMPORT RAPIDE POSTMAN

### Collection JSON (à importer)
Créez une nouvelle collection et importez ce JSON :

```json
{
    "info": {
        "name": "ClockIn API Enterprise",
        "description": "Collection complète pour tester l'API ClockIn optimisée",
        "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
    },
    "variable": [
        {
            "key": "base_url",
            "value": "http://localhost:8080/api"
        }
    ]
}
```

### Commandes cURL Alternatives

#### Login Admin :
```bash
curl -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Health Check :
```bash
curl -X GET "http://localhost:8080/api/health" \
  -H "Accept: application/json"
```

#### Pointage :
```bash
curl -X POST "http://localhost:8080/api/pointage" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

#### Créer un Site :
```bash
curl -X POST "http://localhost:8080/api/sites" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -d '{
    "name": "Nouveau Chantier",
    "latitude": 33.5731,
    "longitude": -7.5898
  }'
```

#### Métriques de Monitoring :
```bash
curl -X GET "http://localhost:8080/api/monitoring/metrics" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

---

## ✅ 11. CHECKLIST DE TEST COMPLÈTE

### Tests d'Authentification
- [ ] Login Admin réussi (200)
- [ ] Login Employé réussi (200)
- [ ] Login avec mauvais credentials (401)
- [ ] Logout réussi (200)
- [ ] Accès sans token (401)
- [ ] Token expiré (401)

### Tests de Pointage
- [ ] Vérification localisation dans la zone (200)
- [ ] Vérification localisation hors zone (200 avec can_pointe: false)
- [ ] Début de pointage (201)
- [ ] Fin de pointage (200)
- [ ] Pointage sans être assigné à un site (403)
- [ ] Pointage hors zone (400)

### Tests de Gestion des Sites (Admin)
- [ ] Liste des sites (200)
- [ ] Création de site (201)
- [ ] Modification de site (200)
- [ ] Suppression de site (200)
- [ ] Détails d'un site (200)
- [ ] Assignment site-employé (200)
- [ ] Accès employé aux sites (403)

### Tests de Gestion des Employés (Admin)
- [ ] Liste des employés (200)
- [ ] Création d'employé (201)
- [ ] Modification d'employé (200)
- [ ] Suppression d'employé (200)
- [ ] Détails d'un employé (200)
- [ ] Accès employé aux employés (403)

### Tests de Monitoring
- [ ] Health check public (200)
- [ ] Métriques admin (200)
- [ ] Statut système admin (200)
- [ ] Dashboard admin (200)
- [ ] Accès employé au monitoring (403)

### Tests de Performance
- [ ] Temps de réponse < 2000ms
- [ ] Health check < 500ms
- [ ] Login < 1000ms
- [ ] Pointage < 1000ms
- [ ] Pas d'erreurs 500

### Tests de Sécurité
- [ ] Rate limiting sur login (429)
- [ ] Headers de sécurité présents
- [ ] Protection CSRF
- [ ] Validation des données
- [ ] Logs de sécurité générés

### Tests de Validation
- [ ] Email requis pour login (422)
- [ ] Password requis pour login (422)
- [ ] Latitude/longitude requises pour pointage (422)
- [ ] Nom requis pour site (422)
- [ ] Format email valide (422)

---

## 🎯 12. CONSEILS DE TEST

### Bonnes Pratiques
1. **Testez toujours en ordre** : Auth → Création → Utilisation → Suppression
2. **Vérifiez les headers** de réponse pour la sécurité
3. **Testez les cas d'erreur** autant que les cas de succès
4. **Mesurez les performances** avec les métriques de temps
5. **Utilisez les variables** Postman pour éviter la duplication

### Variables Utiles
```
{{base_url}} = http://localhost:8080/api
{{admin_token}} = Token admin après login
{{employee_token}} = Token employé après login
{{site_id}} = ID du site créé
{{user_id}} = ID de l'utilisateur
```

### Tests Automatisés
Utilisez les scripts de test Postman pour automatiser la validation des réponses et la gestion des tokens.

---

## 📞 Support

En cas de problème :
1. Vérifiez les logs : `storage/logs/laravel.log`
2. Testez le health check : `GET /api/health`
3. Vérifiez la configuration : `.env`
4. Consultez la documentation : `README.md`

**Bonne chance avec vos tests ! 🚀**
