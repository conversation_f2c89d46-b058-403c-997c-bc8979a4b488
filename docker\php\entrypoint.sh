#!/bin/sh

set -e

# Fonction de logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

log "Starting ClockIn application container..."

# Vérifier les variables d'environnement requises
if [ -z "$APP_ENV" ]; then
    export APP_ENV=production
fi

log "Environment: $APP_ENV"

# Attendre que MySQL soit prêt
if [ "$CONTAINER_ROLE" = "app" ] || [ "$CONTAINER_ROLE" = "queue" ] || [ "$CONTAINER_ROLE" = "scheduler" ]; then
    log "Waiting for MySQL to be ready..."
    while ! nc -z mysql 3306; do
        sleep 1
    done
    log "MySQL is ready!"

    # Attendre que Redis soit prêt
    log "Waiting for Redis to be ready..."
    while ! nc -z redis 6379; do
        sleep 1
    done
    log "Redis is ready!"
fi

# Exécuter les migrations et optimisations selon le rôle du conteneur
case "$CONTAINER_ROLE" in
    "app")
        log "Configuring application container..."
        
        # Générer la clé d'application si elle n'existe pas
        if [ -z "$APP_KEY" ]; then
            log "Generating application key..."
            php artisan key:generate --force
        fi
        
        # Exécuter les migrations
        log "Running database migrations..."
        php artisan migrate --force
        
        # Optimiser pour la production
        if [ "$APP_ENV" = "production" ]; then
            log "Optimizing for production..."
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            php artisan event:cache
        fi
        
        # Créer les liens symboliques pour le stockage
        log "Creating storage link..."
        php artisan storage:link
        
        # Définir les permissions
        log "Setting permissions..."
        chown -R www-data:www-data /var/www/html/storage
        chown -R www-data:www-data /var/www/html/bootstrap/cache
        chmod -R 775 /var/www/html/storage
        chmod -R 775 /var/www/html/bootstrap/cache
        
        log "Application container ready!"
        ;;
        
    "queue")
        log "Configuring queue worker container..."
        
        # Attendre que l'application soit prête
        sleep 10
        
        log "Starting queue worker..."
        exec php artisan queue:work redis --sleep=3 --tries=3 --max-time=3600 --memory=512
        ;;
        
    "scheduler")
        log "Configuring scheduler container..."
        
        # Attendre que l'application soit prête
        sleep 15
        
        log "Starting scheduler..."
        exec php artisan schedule:work
        ;;
        
    *)
        log "Default container role, starting PHP-FPM..."
        ;;
esac

# Démarrer le processus principal
log "Starting main process: $@"
exec "$@"
