<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Carbon\Carbon;

class LogsCleanCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'logs:clean 
                            {--days=30 : Number of days to keep logs}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean old log files to free up disk space';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        
        $this->info("Cleaning logs older than {$days} days...");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be deleted');
        }

        $cutoffDate = Carbon::now()->subDays($days);
        $totalSize = 0;
        $totalFiles = 0;

        // Répertoires de logs à nettoyer
        $logDirectories = [
            storage_path('logs'),
            storage_path('logs/supervisor'),
            '/var/log/nginx',
            '/var/log/php',
            '/var/log/mysql',
            '/var/log/redis',
        ];

        foreach ($logDirectories as $directory) {
            if (!File::exists($directory)) {
                continue;
            }

            $this->line("Checking directory: {$directory}");
            
            $files = File::allFiles($directory);
            
            foreach ($files as $file) {
                $fileDate = Carbon::createFromTimestamp($file->getMTime());
                
                if ($fileDate->lt($cutoffDate)) {
                    $fileSize = $file->getSize();
                    $totalSize += $fileSize;
                    $totalFiles++;
                    
                    $this->line("  - {$file->getFilename()} ({$this->formatBytes($fileSize)}) - {$fileDate->format('Y-m-d H:i:s')}");
                    
                    if (!$dryRun) {
                        try {
                            File::delete($file->getPathname());
                            $this->info("    Deleted: {$file->getFilename()}");
                        } catch (\Exception $e) {
                            $this->error("    Failed to delete {$file->getFilename()}: {$e->getMessage()}");
                        }
                    }
                }
            }
        }

        // Nettoyer les logs Laravel spécifiques
        $this->cleanLaravelLogs($cutoffDate, $dryRun, $totalSize, $totalFiles);

        // Nettoyer les logs rotatifs
        $this->cleanRotatedLogs($cutoffDate, $dryRun, $totalSize, $totalFiles);

        // Résumé
        $this->line('');
        $this->info('=== Cleanup Summary ===');
        $this->line("Files processed: {$totalFiles}");
        $this->line("Total size: {$this->formatBytes($totalSize)}");
        
        if ($dryRun) {
            $this->warn('No files were actually deleted (dry run mode)');
        } else {
            $this->info('Cleanup completed successfully');
        }

        return 0;
    }

    /**
     * Nettoie les logs Laravel spécifiques
     */
    private function cleanLaravelLogs(Carbon $cutoffDate, bool $dryRun, int &$totalSize, int &$totalFiles): void
    {
        $logPath = storage_path('logs');
        
        // Logs quotidiens Laravel
        $pattern = $logPath . '/laravel-*.log';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            // Extraire la date du nom du fichier
            if (preg_match('/laravel-(\d{4}-\d{2}-\d{2})\.log$/', $file, $matches)) {
                try {
                    $fileDate = Carbon::createFromFormat('Y-m-d', $matches[1]);
                    
                    if ($fileDate->lt($cutoffDate)) {
                        $fileSize = filesize($file);
                        $totalSize += $fileSize;
                        $totalFiles++;
                        
                        $this->line("  - " . basename($file) . " ({$this->formatBytes($fileSize)}) - {$fileDate->format('Y-m-d')}");
                        
                        if (!$dryRun) {
                            unlink($file);
                            $this->info("    Deleted: " . basename($file));
                        }
                    }
                } catch (\Exception $e) {
                    $this->error("Error processing {$file}: {$e->getMessage()}");
                }
            }
        }
    }

    /**
     * Nettoie les logs rotatifs
     */
    private function cleanRotatedLogs(Carbon $cutoffDate, bool $dryRun, int &$totalSize, int &$totalFiles): void
    {
        $patterns = [
            '/var/log/nginx/*.log.*',
            '/var/log/mysql/*.log.*',
            '/var/log/redis/*.log.*',
            storage_path('logs') . '/*.log.*',
        ];

        foreach ($patterns as $pattern) {
            $files = glob($pattern);
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    $fileDate = Carbon::createFromTimestamp(filemtime($file));
                    
                    if ($fileDate->lt($cutoffDate)) {
                        $fileSize = filesize($file);
                        $totalSize += $fileSize;
                        $totalFiles++;
                        
                        $this->line("  - " . basename($file) . " ({$this->formatBytes($fileSize)}) - {$fileDate->format('Y-m-d H:i:s')}");
                        
                        if (!$dryRun) {
                            unlink($file);
                            $this->info("    Deleted: " . basename($file));
                        }
                    }
                }
            }
        }
    }

    /**
     * Formate les bytes en format lisible
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
