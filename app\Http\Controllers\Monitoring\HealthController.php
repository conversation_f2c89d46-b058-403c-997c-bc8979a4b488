<?php

namespace App\Http\Controllers\Monitoring;

use App\Http\Controllers\Controller;
use App\Services\PerformanceMonitor;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

/**
 * @group Monitoring
 *
 * APIs pour le monitoring et la santé de l'application
 */
class HealthController extends Controller
{
    private PerformanceMonitor $monitor;

    public function __construct(PerformanceMonitor $monitor)
    {
        $this->monitor = $monitor;
    }

    /**
     * Health Check
     *
     * Vérifie la santé générale de l'application
     *
     * @response 200 {
     *   "status": "healthy",
     *   "timestamp": "2025-01-15T10:30:00Z",
     *   "checks": {
     *     "database": {"status": "healthy", "response_time": "5.2ms"},
     *     "redis": {"status": "healthy", "response_time": "1.1ms"},
     *     "disk": {"status": "healthy", "usage": "45%"},
     *     "memory": {"status": "healthy", "usage": "67%"}
     *   }
     * }
     */
    public function health(): JsonResponse
    {
        $health = $this->monitor->healthCheck();
        
        $statusCode = match ($health['status']) {
            'healthy' => 200,
            'warning' => 200,
            'unhealthy' => 503,
            default => 500,
        };

        return response()->json($health, $statusCode);
    }

    /**
     * Performance Metrics
     *
     * Obtient les métriques de performance actuelles
     *
     * @authenticated
     *
     * @response 200 {
     *   "timestamp": "2025-01-15T10:30:00Z",
     *   "global": {
     *     "total_duration": 1250.5,
     *     "total_memory_used": 12582912,
     *     "memory_peak": 15728640,
     *     "queries_count": 15,
     *     "cache_hits": {"hits": 1250, "misses": 45, "hit_rate": 96.5}
     *   },
     *   "database": {
     *     "mysql": {"status": "connected", "server_info": "8.0.32"}
     *   },
     *   "redis": {
     *     "status": "connected",
     *     "version": "7.0.8",
     *     "memory_used": "2.5M",
     *     "connected_clients": 5
     *   }
     * }
     */
    public function metrics(Request $request): JsonResponse
    {
        // Vérifier les permissions admin
        if (!$request->user() || !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.',
                'message_ar' => 'تم رفض الوصول. مطلوب صلاحيات المدير.'
            ], 403);
        }

        $globalMetrics = $this->monitor->getGlobalMetrics();
        $databaseMetrics = $this->monitor->monitorDatabase();
        $redisMetrics = $this->monitor->monitorRedis();

        $metrics = [
            'timestamp' => now()->toISOString(),
            'global' => $globalMetrics,
            'database' => $databaseMetrics,
            'redis' => $redisMetrics,
        ];

        return response()->json($metrics);
    }

    /**
     * System Status
     *
     * Obtient le statut détaillé du système
     *
     * @authenticated
     *
     * @response 200 {
     *   "application": {
     *     "name": "ClockIn",
     *     "version": "1.0.0",
     *     "environment": "production",
     *     "debug": false,
     *     "uptime": 86400
     *   },
     *   "server": {
     *     "php_version": "8.2.0",
     *     "memory_limit": "512M",
     *     "max_execution_time": 300,
     *     "opcache_enabled": true
     *   },
     *   "services": {
     *     "database": "connected",
     *     "redis": "connected",
     *     "queue": "running"
     *   }
     * }
     */
    public function status(Request $request): JsonResponse
    {
        // Vérifier les permissions admin
        if (!$request->user() || !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.',
                'message_ar' => 'تم رفض الوصول. مطلوب صلاحيات المدير.'
            ], 403);
        }

        $status = [
            'application' => [
                'name' => config('app.name'),
                'version' => '1.0.0', // À récupérer depuis composer.json ou config
                'environment' => config('app.env'),
                'debug' => config('app.debug'),
                'uptime' => $this->getApplicationUptime(),
            ],
            'server' => [
                'php_version' => PHP_VERSION,
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'opcache_enabled' => extension_loaded('opcache') && opcache_get_status()['opcache_enabled'] ?? false,
                'redis_extension' => extension_loaded('redis'),
                'pdo_mysql' => extension_loaded('pdo_mysql'),
            ],
            'services' => [
                'database' => $this->checkDatabaseConnection(),
                'redis' => $this->checkRedisConnection(),
                'queue' => $this->checkQueueStatus(),
                'storage' => $this->checkStorageStatus(),
            ],
        ];

        return response()->json($status);
    }

    /**
     * Performance Dashboard Data
     *
     * Obtient les données pour le dashboard de performance
     *
     * @authenticated
     */
    public function dashboard(Request $request): JsonResponse
    {
        // Vérifier les permissions admin
        if (!$request->user() || !$request->user()->isAdmin()) {
            return response()->json([
                'success' => false,
                'message' => 'Accès refusé. Droits administrateur requis.',
                'message_ar' => 'تم رفض الوصول. مطلوب صلاحيات المدير.'
            ], 403);
        }

        // Récupérer les métriques récentes depuis le cache
        $recentMetrics = Cache::get('performance:latest', []);
        
        // Récupérer les alertes actives
        $alerts = $this->getActiveAlerts();
        
        // Statistiques des dernières 24h
        $stats24h = $this->getStats24h();

        return response()->json([
            'current_metrics' => $recentMetrics,
            'active_alerts' => $alerts,
            'stats_24h' => $stats24h,
            'health_status' => $this->monitor->healthCheck(),
        ]);
    }

    /**
     * Obtient l'uptime de l'application
     */
    private function getApplicationUptime(): int
    {
        $uptimeFile = storage_path('framework/cache/app_start_time');
        
        if (!file_exists($uptimeFile)) {
            file_put_contents($uptimeFile, time());
            return 0;
        }
        
        $startTime = (int) file_get_contents($uptimeFile);
        return time() - $startTime;
    }

    /**
     * Vérifie la connexion à la base de données
     */
    private function checkDatabaseConnection(): string
    {
        try {
            \DB::connection()->getPdo();
            return 'connected';
        } catch (\Exception $e) {
            return 'disconnected';
        }
    }

    /**
     * Vérifie la connexion Redis
     */
    private function checkRedisConnection(): string
    {
        try {
            Cache::store('redis')->getRedis()->ping();
            return 'connected';
        } catch (\Exception $e) {
            return 'disconnected';
        }
    }

    /**
     * Vérifie le statut des queues
     */
    private function checkQueueStatus(): string
    {
        try {
            // Vérifier si des jobs sont en cours de traitement
            $redis = Cache::store('redis')->getRedis();
            $queueSize = $redis->llen('queues:default');
            return $queueSize !== false ? 'running' : 'stopped';
        } catch (\Exception $e) {
            return 'unknown';
        }
    }

    /**
     * Vérifie le statut du stockage
     */
    private function checkStorageStatus(): string
    {
        $storagePath = storage_path();
        return is_writable($storagePath) ? 'writable' : 'read-only';
    }

    /**
     * Obtient les alertes actives
     */
    private function getActiveAlerts(): array
    {
        $alerts = [];
        $alertKeys = Cache::get('active_alert_keys', []);
        
        foreach ($alertKeys as $key) {
            $alert = Cache::get($key);
            if ($alert) {
                $alerts[] = $alert;
            }
        }
        
        return $alerts;
    }

    /**
     * Obtient les statistiques des dernières 24h
     */
    private function getStats24h(): array
    {
        // Ici, vous pourriez implémenter une logique pour récupérer
        // les statistiques depuis une base de données de métriques
        return [
            'total_requests' => Cache::get('stats:24h:requests', 0),
            'average_response_time' => Cache::get('stats:24h:avg_response_time', 0),
            'error_rate' => Cache::get('stats:24h:error_rate', 0),
            'peak_memory_usage' => Cache::get('stats:24h:peak_memory', 0),
        ];
    }
}
