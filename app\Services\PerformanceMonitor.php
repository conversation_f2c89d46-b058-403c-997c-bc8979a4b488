<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PerformanceMonitor
{
    private array $metrics = [];
    private float $startTime;
    private int $startMemory;

    public function __construct()
    {
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
    }

    /**
     * Démarre le monitoring d'une opération
     */
    public function start(string $operation): void
    {
        $this->metrics[$operation] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
        ];
    }

    /**
     * Termine le monitoring d'une opération
     */
    public function end(string $operation): array
    {
        if (!isset($this->metrics[$operation])) {
            return [];
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        $metrics = [
            'operation' => $operation,
            'duration' => round(($endTime - $this->metrics[$operation]['start_time']) * 1000, 2), // ms
            'memory_used' => $endMemory - $this->metrics[$operation]['start_memory'],
            'memory_peak' => memory_get_peak_usage(true),
            'timestamp' => now()->toISOString(),
        ];

        // Log si l'opération est lente
        if ($metrics['duration'] > config('performance.monitoring.response_time_warning', 2000)) {
            Log::channel('performance')->warning('Slow operation detected', $metrics);
        }

        unset($this->metrics[$operation]);
        return $metrics;
    }

    /**
     * Obtient les métriques de performance globales
     */
    public function getGlobalMetrics(): array
    {
        $currentTime = microtime(true);
        $currentMemory = memory_get_usage(true);

        return [
            'total_duration' => round(($currentTime - $this->startTime) * 1000, 2),
            'total_memory_used' => $currentMemory - $this->startMemory,
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => ini_get('memory_limit'),
            'queries_count' => $this->getQueriesCount(),
            'cache_hits' => $this->getCacheHits(),
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Monitore l'utilisation de la base de données
     */
    public function monitorDatabase(): array
    {
        $connections = [];
        
        foreach (config('database.connections') as $name => $config) {
            if ($config['driver'] === 'mysql') {
                try {
                    $pdo = DB::connection($name)->getPdo();
                    $connections[$name] = [
                        'status' => 'connected',
                        'server_info' => $pdo->getAttribute(\PDO::ATTR_SERVER_INFO),
                        'connection_status' => $pdo->getAttribute(\PDO::ATTR_CONNECTION_STATUS),
                    ];
                } catch (\Exception $e) {
                    $connections[$name] = [
                        'status' => 'error',
                        'error' => $e->getMessage(),
                    ];
                }
            }
        }

        return $connections;
    }

    /**
     * Monitore l'utilisation de Redis
     */
    public function monitorRedis(): array
    {
        $redis = [];
        
        try {
            $redisConnection = Cache::store('redis')->getRedis();
            $info = $redisConnection->info();
            
            $redis = [
                'status' => 'connected',
                'version' => $info['redis_version'] ?? 'unknown',
                'memory_used' => $info['used_memory_human'] ?? 'unknown',
                'connected_clients' => $info['connected_clients'] ?? 0,
                'total_commands_processed' => $info['total_commands_processed'] ?? 0,
                'keyspace_hits' => $info['keyspace_hits'] ?? 0,
                'keyspace_misses' => $info['keyspace_misses'] ?? 0,
            ];
        } catch (\Exception $e) {
            $redis = [
                'status' => 'error',
                'error' => $e->getMessage(),
            ];
        }

        return $redis;
    }

    /**
     * Obtient le nombre de requêtes exécutées
     */
    private function getQueriesCount(): int
    {
        return collect(DB::getQueryLog())->count();
    }

    /**
     * Obtient les statistiques de cache
     */
    private function getCacheHits(): array
    {
        try {
            $redis = Cache::store('redis')->getRedis();
            $info = $redis->info();
            
            return [
                'hits' => $info['keyspace_hits'] ?? 0,
                'misses' => $info['keyspace_misses'] ?? 0,
                'hit_rate' => $this->calculateHitRate(
                    $info['keyspace_hits'] ?? 0,
                    $info['keyspace_misses'] ?? 0
                ),
            ];
        } catch (\Exception $e) {
            return [
                'hits' => 0,
                'misses' => 0,
                'hit_rate' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Calcule le taux de réussite du cache
     */
    private function calculateHitRate(int $hits, int $misses): float
    {
        $total = $hits + $misses;
        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }

    /**
     * Enregistre les métriques de performance
     */
    public function logMetrics(array $metrics): void
    {
        if (config('performance.logging.performance_logs')) {
            Log::channel('performance')->info('Performance metrics', $metrics);
        }

        // Stocker en cache pour le dashboard
        Cache::put('performance:latest', $metrics, 300); // 5 minutes
    }

    /**
     * Vérifie les seuils d'alerte
     */
    public function checkAlerts(array $metrics): array
    {
        $alerts = [];

        // Vérifier le temps de réponse
        if ($metrics['total_duration'] > config('performance.monitoring.response_time_warning', 2000)) {
            $alerts[] = [
                'type' => 'response_time',
                'message' => "Response time too high: {$metrics['total_duration']}ms",
                'severity' => 'warning',
            ];
        }

        // Vérifier l'utilisation mémoire
        $memoryUsagePercent = ($metrics['memory_peak'] / $this->parseMemoryLimit()) * 100;
        if ($memoryUsagePercent > config('performance.monitoring.memory_limit_warning', 80)) {
            $alerts[] = [
                'type' => 'memory_usage',
                'message' => "Memory usage too high: {$memoryUsagePercent}%",
                'severity' => 'warning',
            ];
        }

        return $alerts;
    }

    /**
     * Parse la limite de mémoire PHP
     */
    private function parseMemoryLimit(): int
    {
        $limit = ini_get('memory_limit');
        if ($limit === '-1') {
            return PHP_INT_MAX;
        }

        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }

    /**
     * Monitore la santé de l'application
     */
    public function healthCheck(): array
    {
        $health = [
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'checks' => [],
        ];

        // Vérifier la base de données
        try {
            DB::connection()->getPdo();
            $health['checks']['database'] = ['status' => 'healthy', 'response_time' => $this->measureDatabaseResponseTime()];
        } catch (\Exception $e) {
            $health['checks']['database'] = ['status' => 'unhealthy', 'error' => $e->getMessage()];
            $health['status'] = 'unhealthy';
        }

        // Vérifier Redis
        try {
            $redis = Cache::store('redis')->getRedis();
            $start = microtime(true);
            $redis->ping();
            $responseTime = round((microtime(true) - $start) * 1000, 2);
            $health['checks']['redis'] = ['status' => 'healthy', 'response_time' => $responseTime . 'ms'];
        } catch (\Exception $e) {
            $health['checks']['redis'] = ['status' => 'unhealthy', 'error' => $e->getMessage()];
            $health['status'] = 'unhealthy';
        }

        // Vérifier l'espace disque
        $diskUsage = $this->getDiskUsage();
        if ($diskUsage['percentage'] > 90) {
            $health['checks']['disk'] = ['status' => 'warning', 'usage' => $diskUsage['percentage'] . '%'];
            if ($health['status'] === 'healthy') {
                $health['status'] = 'warning';
            }
        } else {
            $health['checks']['disk'] = ['status' => 'healthy', 'usage' => $diskUsage['percentage'] . '%'];
        }

        // Vérifier la mémoire
        $memoryUsage = (memory_get_usage(true) / $this->parseMemoryLimit()) * 100;
        if ($memoryUsage > 90) {
            $health['checks']['memory'] = ['status' => 'warning', 'usage' => round($memoryUsage, 2) . '%'];
            if ($health['status'] === 'healthy') {
                $health['status'] = 'warning';
            }
        } else {
            $health['checks']['memory'] = ['status' => 'healthy', 'usage' => round($memoryUsage, 2) . '%'];
        }

        return $health;
    }

    /**
     * Mesure le temps de réponse de la base de données
     */
    private function measureDatabaseResponseTime(): string
    {
        $start = microtime(true);
        DB::select('SELECT 1');
        $responseTime = round((microtime(true) - $start) * 1000, 2);
        return $responseTime . 'ms';
    }

    /**
     * Obtient l'utilisation du disque
     */
    private function getDiskUsage(): array
    {
        $path = storage_path();
        $total = disk_total_space($path);
        $free = disk_free_space($path);
        $used = $total - $free;

        return [
            'total' => $total,
            'used' => $used,
            'free' => $free,
            'percentage' => $total > 0 ? round(($used / $total) * 100, 2) : 0,
        ];
    }
}
