# Configuration du site ClockIn

# Redirection HTTP vers HTTPS
server {
    listen 80;
    server_name localhost clockin.local;
    return 301 https://$server_name$request_uri;
}

# Configuration HTTPS principale
server {
    listen 443 ssl http2;
    server_name localhost clockin.local;
    root /var/www/html/public;
    index index.php index.html index.htm;

    # Configuration SSL
    ssl_certificate /etc/nginx/ssl/clockin.crt;
    ssl_certificate_key /etc/nginx/ssl/clockin.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Configuration des logs
    access_log /var/log/nginx/clockin_access.log main;
    error_log /var/log/nginx/clockin_error.log;

    # Configuration du rate limiting
    limit_req zone=api burst=20 nodelay;

    # Configuration des headers de sécurité
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Configuration des fichiers statiques
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt|tar|woff|svg|ttf|eot|woff2)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
        
        # Compression pour les fichiers statiques
        gzip_static on;
        
        # Essayer le fichier, sinon passer à Laravel
        try_files $uri $uri/ @laravel;
    }

    # Configuration pour les fichiers de documentation
    location /docs {
        try_files $uri $uri/ /docs/index.html;
        
        # Cache pour la documentation
        expires 1h;
        add_header Cache-Control "public";
    }

    # Configuration pour l'API
    location /api {
        # Rate limiting spécifique pour l'API
        limit_req zone=api burst=30 nodelay;
        
        # Headers pour l'API
        add_header X-API-Version "1.0" always;
        
        try_files $uri $uri/ @laravel;
    }

    # Configuration pour les routes de login
    location ~ ^/api/auth/login {
        limit_req zone=login burst=3 nodelay;
        try_files $uri $uri/ @laravel;
    }

    # Configuration pour le monitoring
    location /status {
        access_log off;
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        
        fastcgi_pass app:9000;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Configuration pour le health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Configuration principale pour Laravel
    location / {
        try_files $uri $uri/ @laravel;
    }

    # Configuration pour les fichiers PHP
    location @laravel {
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root/index.php;
        include fastcgi_params;
        
        # Configuration des buffers FastCGI
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        
        # Configuration du cache FastCGI
        fastcgi_cache_bypass $skip_cache;
        fastcgi_no_cache $skip_cache;
        fastcgi_cache_valid 200 60m;
        fastcgi_cache_valid 404 1m;
    }

    # Bloquer l'accès aux fichiers sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.(?:htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Bloquer l'accès aux répertoires sensibles
    location ~ ^/(storage|bootstrap|config|database|resources|tests|vendor)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
